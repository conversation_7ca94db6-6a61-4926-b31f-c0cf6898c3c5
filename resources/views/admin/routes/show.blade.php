@extends('layouts.admin')

@section('title', __('messages.route_details'))

@section('content')
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">
                <i
                    class="fas fa-route {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ __('messages.route_details') }}:
                {{ $route->name }}
            </h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <a href="{{ route('admin.routes.index') }}"
                    class="btn btn-outline-secondary {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}">
                    <i
                        class="fas fa-arrow-left {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.back_to_routes') }}
                </a>
                @can('update admin_routes')
                    <a href="{{ route('admin.routes.edit', $route) }}" class="btn btn-primary">
                        <i
                            class="fas fa-edit {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.edit_route') }}
                    </a>
                @endcan
            </div>
        </div>

        @if (session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i
                    class="fas fa-check-circle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        @endif

        <div class="row">
            <!-- Route Information -->
            <div class="col-md-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i
                                class="fas fa-info-circle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ __('messages.route_information') }}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">{{ __('messages.route_name') }}:</label>
                                    <p class="mb-0">{{ $route->name }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">{{ __('messages.status') }}:</label>
                                    <p class="mb-0">
                                        <span class="badge {{ $route->status_badge }}">
                                            {{ __('messages.' . $route->status) }}
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">{{ __('messages.from_area') }}:</label>
                                    <p class="mb-0">{{ $route->from_area }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">{{ __('messages.to_school') }}:</label>
                                    <p class="mb-0">{{ $route->to_school }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">{{ __('messages.pickup_time') }}:</label>
                                    <p class="mb-0">
                                        {{ $route->pickup_time ? $route->pickup_time->format('h:i A') : __('messages.not_set') }}
                                    </p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">{{ __('messages.dropoff_time') }}:</label>
                                    <p class="mb-0">
                                        {{ $route->dropoff_time ? $route->dropoff_time->format('h:i A') : __('messages.not_set') }}
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">{{ __('messages.monthly_price') }}:</label>
                                    <p class="mb-0">EGP {{ number_format($route->monthly_price, 2) }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">{{ __('messages.created') }}:</label>
                                    <p class="mb-0">{{ $route->created_at->format('F j, Y') }}</p>
                                </div>
                            </div>
                        </div>

                        @if ($route->description)
                            <div class="mb-3">
                                <label class="form-label fw-bold">{{ __('messages.description') }}:</label>
                                <p class="mb-0">{{ $route->description }}</p>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Route Stops -->
                @if ($route->stops && count($route->stops) > 0)
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i
                                    class="fas fa-map-marker-alt {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ __('messages.route_stops') }}
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="route-stops">
                                @foreach ($route->stops as $index => $stop)
                                    <div class="stop-item d-flex align-items-center mb-3">
                                        <div class="stop-number">
                                            <span class="badge bg-primary rounded-circle">{{ $index + 1 }}</span>
                                        </div>
                                        <div class="stop-info {{ app()->getLocale() == 'ar' ? 'me-3' : 'ms-3' }}">
                                            <strong>{{ $stop }}</strong>
                                        </div>
                                    </div>
                                    @if ($index < count($route->stops) - 1)
                                        <div class="stop-connector">
                                            <i class="fas fa-arrow-down text-muted"></i>
                                        </div>
                                    @endif
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Current Students -->
                @if ($route->subscriptions->where('status', 'active')->count() > 0)
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i
                                    class="fas fa-graduation-cap {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ __('messages.current_students') }}
                                ({{ $route->subscriptions->where('status', 'active')->count() }})
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>{{ __('messages.client') }}</th>
                                            <th>{{ __('messages.student_name') }}</th>
                                            <th>{{ __('messages.address') }}</th>
                                            <th>{{ __('messages.location') }}</th>
                                            <th>{{ __('messages.subscription_type') }}</th>
                                            <th>{{ __('messages.price') }}</th>
                                            <th>{{ __('messages.start_date') }}</th>
                                            <th>{{ __('messages.status') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($route->subscriptions->where('status', 'active') as $subscription)
                                            <tr>
                                                <td>{{ $subscription->student->name ?? '' }}</td>
                                                <td>{{ $subscription->client->user->name ?? '' }}</td>
                                                <td>{{ $subscription->client->address ?? '' }} -
                                                    {{ $subscription->client->area ?? '' }}</td>
                                                <td>
                                                    @php
                                                        $location = $subscription->client->location ?? '';
                                                        if (!empty($location)) {
                                                            [$lat, $lng] = explode(',', $location);
                                                            $location = "https://www.google.com/maps/search/?api=1&query=$lat,$lng";
                                                        } else {
                                                            $location = '#';
                                                        }
                                                    @endphp
                                                    <a href="{{ $location }}" target="_blank">
                                                        {{ $subscription->client->location ?? __('messages.not_provided') }}
                                                    </a>
                                                </td>
                                                <td>{{ __('messages.' . $subscription->subscription_type) }}</td>
                                                <td>EGP {{ number_format($subscription->price, 2) }}</td>
                                                <td>{{ $subscription->start_date->format('M j, Y') }}</td>
                                                <td>
                                                    <span class="badge {{ $subscription->status_badge }}">
                                                        {{ __('messages.' . $subscription->status) }}
                                                    </span>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Statistics & Driver Info -->
            <div class="col-md-4">
                <!-- Capacity Statistics -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i
                                class="fas fa-chart-pie {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ __('messages.capacity_statistics') }}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label fw-bold">{{ __('messages.total_capacity') }}:</label>
                            <p class="mb-0">{{ $route->capacity }} {{ __('messages.students') }}</p>
                        </div>

                        <div class="mb-3">
                            <label class="form-label fw-bold">{{ __('messages.current_students') }}:</label>
                            <p class="mb-0">{{ $route->current_students }} {{ __('messages.students') }}</p>
                        </div>

                        <div class="mb-3">
                            <label class="form-label fw-bold">{{ __('messages.available_seats') }}:</label>
                            <p class="mb-0">{{ $route->available_seats }} {{ __('messages.seats') }}</p>
                        </div>

                        <div class="mb-3">
                            <label class="form-label fw-bold">{{ __('messages.capacity_usage') }}:</label>
                            <div class="progress mt-1">
                                <div class="progress-bar" role="progressbar"
                                    style="width: {{ $route->capacity_percentage }}%"
                                    aria-valuenow="{{ $route->capacity_percentage }}" aria-valuemin="0"
                                    aria-valuemax="100">
                                    {{ number_format($route->capacity_percentage, 1) }}%
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Driver Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i
                                class="fas fa-user-tie {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ __('messages.driver_information') }}
                        </h5>
                    </div>
                    <div class="card-body">
                        @php $currentDriver = $route->currentDriver(); @endphp
                        @if ($currentDriver)
                            <div class="mb-3">
                                <label class="form-label fw-bold">{{ __('messages.driver_name') }}:</label>
                                <p class="mb-0">{{ $currentDriver->name }}</p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">{{ __('messages.phone') }}:</label>
                                <p class="mb-0">{{ $currentDriver->phone }}</p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">{{ __('messages.license_number') }}:</label>
                                <p class="mb-0">{{ $currentDriver->license_number }}</p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">{{ __('messages.vehicle_info') }}:</label>
                                <p class="mb-0">{{ $currentDriver->vehicle_info }}</p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">{{ __('messages.status') }}:</label>
                                <p class="mb-0">
                                    <span class="badge {{ $currentDriver->status_badge }}">
                                        {{ __('messages.' . $currentDriver->status) }}
                                    </span>
                                </p>
                            </div>

                            @can('read admin_drivers')
                                <div class="d-grid">
                                    <a href="{{ route('admin.drivers.show', $currentDriver) }}"
                                        class="btn btn-outline-primary btn-sm">
                                        <i
                                            class="fas fa-eye {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.view_driver_details') }}
                                    </a>
                                </div>
                            @endcan
                        @else
                            <div class="text-center text-muted">
                                <i class="fas fa-user-slash fa-3x mb-3"></i>
                                <p>{{ __('messages.no_driver_assigned') }}</p>
                                @can('update admin_routes')
                                    <a href="{{ route('admin.routes.edit', $route) }}"
                                        class="btn btn-outline-primary btn-sm">
                                        <i
                                            class="fas fa-plus {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.assign_driver') }}
                                    </a>
                                @endcan
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Financial Summary -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i
                                class="fas fa-dollar-sign {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ __('messages.financial_summary') }}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label fw-bold">{{ __('messages.active_subscriptions') }}:</label>
                            <p class="mb-0">{{ $route->subscriptions->where('status', 'active')->count() }}</p>
                        </div>

                        <div class="mb-3">
                            <label class="form-label fw-bold">{{ __('messages.monthly_revenue') }}:</label>
                            <p class="mb-0">EGP
                                {{ number_format($route->subscriptions->where('status', 'active')->sum('price'), 2) }}</p>
                        </div>

                        <div class="mb-3">
                            <label class="form-label fw-bold">{{ __('messages.potential_revenue') }}:</label>
                            <p class="mb-0">EGP {{ number_format($route->capacity * $route->monthly_price, 2) }}</p>
                        </div>

                        <div class="mb-3">
                            <label class="form-label fw-bold">{{ __('messages.revenue_efficiency') }}:</label>
                            @php
                                $currentRevenue = $route->subscriptions->where('status', 'active')->sum('price');
                                $potentialRevenue = $route->capacity * $route->monthly_price;
                                $efficiency = $potentialRevenue > 0 ? ($currentRevenue / $potentialRevenue) * 100 : 0;
                            @endphp
                            <div class="progress mt-1">
                                <div class="progress-bar bg-success" role="progressbar"
                                    style="width: {{ $efficiency }}%" aria-valuenow="{{ $efficiency }}"
                                    aria-valuemin="0" aria-valuemax="100">
                                    {{ number_format($efficiency, 1) }}%
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </main>
    </div>
    </div>
@endsection

@push('styles')
    <style>
        .stats-card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: transform 0.2s ease-in-out;
        }

        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        .table-responsive {
            border-radius: 0.375rem;
        }

        .table th {
            border-top: none;
            font-weight: 600;
            background-color: #f8f9fa;
            white-space: nowrap;
        }

        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 1px solid rgba(0, 0, 0, 0.125);
        }

        .form-label {
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .progress {
            height: 20px;
        }

        .route-stops .stop-item {
            position: relative;
        }

        .route-stops .stop-connector {
            text-align: center;
            margin: 0.5rem 0;
            margin-left: 15px;
        }

        .stop-number .badge {
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
        }

        @media (max-width: 768px) {
            .btn-toolbar {
                width: 100%;
                justify-content: center;
            }

            .d-flex.justify-content-between {
                flex-direction: column;
                text-align: center;
            }

            .d-flex.justify-content-between h1 {
                margin-bottom: 1rem;
            }

            .row.g-3 .col-lg-3 {
                margin-bottom: 0.5rem;
            }
        }
    </style>
@endpush
